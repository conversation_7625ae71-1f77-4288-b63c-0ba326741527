import { NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { Router} from "@angular/router";


@Component({
  selector: 'app-server-error',
  standalone: true,
  imports: [NgIf],
  templateUrl: './server-error.component.html',
  styleUrl: './server-error.component.css'
})
export class ServerErrorComponent {

  error: any;

  private readonly _router: Router;

  constructor(router: Router) {
    const navigation = router.getCurrentNavigation();
    this.error = navigation?.extras?.state?.["error"];
    this._router = router;
  }


}
