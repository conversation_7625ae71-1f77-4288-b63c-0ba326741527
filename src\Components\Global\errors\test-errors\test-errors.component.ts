import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, inject } from '@angular/core';
import { environment } from '../../../../Config/environments/environment';

@Component({
  selector: 'app-test-errors',
  standalone: true,
  imports: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],
  templateUrl: './test-errors.component.html',
  styleUrl: './test-errors.component.css'
})
export class TestErrorsComponent {
  
  private baseURL: string = environment.apiUrl;
  private _http = inject(HttpClient);
  public validationErrors: string[] = [];


  get400Error() {
    this._http.get(`${this.baseURL}/buggy/bad-request`).subscribe({
      next: response => console.log(response),
      error: error => console.log(error)
    })
  }

  get401Error() {
    this._http.get(`${this.baseURL}/buggy/auth`).subscribe({
      next: response => console.log(response),
      error: error => console.log(error)
    })
  }


  get404Error() {
    this._http.get(`${this.baseURL}/buggy/not-found`).subscribe({
      next: response => console.log(response),
      error: error => console.log(error)
    })
  }


  get500Error() {
    this._http.get(`${this.baseURL}/buggy/server-error`).subscribe({
      next: response => console.log(response),
      error: error => console.log(error)
    })
  }


  get400ValidationError() {
    this._http.post(`${this.baseURL}/account/register`, {}).subscribe({
      next: response => console.log(response),
      error: error => {
        console.log(error);
        this.validationErrors = error;
      }
    })
  }
}
