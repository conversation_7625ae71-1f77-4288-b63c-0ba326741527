<div>
    <button class="btn btn-outline-primary me-3" (click)="get400Error()">Test 400 error</button>
    <button class="btn btn-outline-primary me-3" (click)="get401Error()">Test 401 error</button>
    <button class="btn btn-outline-primary me-3" (click)="get404Error()">Test 404 error</button>
    <button class="btn btn-outline-primary me-3" (click)="get500Error()">Test 500 error</button>
    <button class="btn btn-outline-primary me-3" (click)="get400ValidationError()">Test 400 validation error</button>

    <ng-container *ngIf="validationErrors.length > 0">
        <div class="row mt-5">
            <ul class="text-danger" *ngFor="let error of validationErrors">
                <li>{{error}}</li>
            </ul>
        </div>
    </ng-container>
</div>