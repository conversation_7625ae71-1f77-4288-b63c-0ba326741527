# InsizonAngular
This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 18.2.15.




## Helpful Commands
1. Development server
Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.
2. Code scaffolding
Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.
3. Build
Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.
4. Running unit tests
Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).
5. Running end-to-end tests
Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.
6. Further help
To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.



# How to start the project
1. nvm use 18.17.0
2. npm run dev
3. Navigate to `http://localhost:4200/




## Note
1. Project must be ran using node version 18.17.0
nvm ls
nvm uninstall <version>
nvm install <version>
nvm use <version>



## Lang
1. Terraform - Aws, Azure, GCP
2. Docker
3. 



## Template Driven Form or Reactive Form
1. Perfer - Reactive form for it being typed and many benefits



# Links
1. How To Setup ESLint and Prettier in Angular 16
https://blog.bitsrc.io/how-ive-set-up-eslint-and-prettier-in-angular-16-and-why-i-did-that-4bfc304284a6
https://www.google.com/search?q=angular+unused-imports%2Fno-unused-imports&rlz=1C5CHFA_enUS1088US1089&oq=angular+unused-imports%2Fno-unused-imports&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBCTE3MDFqMGoxNagCALACAA&sourceid=chrome&ie=UTF-8
2. How to deploy to cloud