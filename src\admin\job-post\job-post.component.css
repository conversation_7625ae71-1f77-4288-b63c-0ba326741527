.add-btn {
    background: var(--darkgold-color);
    color: #fff;
}
.modal-header {
    background-color: var(--darkgold-color);
}
.btn-close-white {
    opacity: 1;
}
.post-btn {
    background-color: var(--darkgold-color);
    color: #fff;
    border: none;
}
.form-label {
    font-size: 14px;
}
.job-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    margin: 20px 0;
}
.job-info li {
    flex: 0 0 47%;
    display: flex;
    gap: 6px;
    font-size: 15px;
}
.job-info li i {
    margin-top: 4px;
}
.job-info li.require-full {
    flex: 0 0 100%;
}

.job-post-grid .card-body {
    box-shadow: none;
    border-radius: 12px;
}
.job-post-grid .col:nth-child(5n+1) .card-body {
  background: #ffe1cb;
}
.job-post-grid .col:nth-child(5n+2) .card-body {
  background: #d5f6ed;
}
.job-post-grid .col:nth-child(5n+3) .card-body {
  background: #e2dbf9;
}
.job-post-grid .col:nth-child(5n+4) .card-body {
  background: #e0f3ff;
}
.job-post-grid .col:nth-child(5n+5) .card-body {
  background: #fbe2f3;
}
.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 6px 9px;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Edit Button */
.edit-btn {
  color: #007bff;
  border-color: #007bff;
}

.edit-btn:hover {
  background-color: #007bff;
  color: #fff;
}

/* Delete Button */
.delete-btn {
  color: #dc3545;
  border: 1px solid #dc3545;
}

.delete-btn:hover {
  background-color: #dc3545;
  color: #fff;
}
