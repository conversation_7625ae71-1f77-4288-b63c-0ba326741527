:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.careers-container {
  min-height: 100vh;
}
.file-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease, background-color 0.3s ease;
  position: relative;
}
.file-drop-zone p {
  font-size: 15px;
}

.file-drop-zone.drag-active {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.file-drop-zone .drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.file-drop-zone .upload-icon {
  font-size: 2rem;
}

.file-drop-zone .file-input {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  top: 0;
  left: 0;
}

.file-help {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

/* Hero Section */
.careers-hero {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.title-word:nth-child(1) {
  animation-delay: 0.2s;
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}


/* Main Content */
.careers-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.filters-title {
  margin: 0;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 2px solid var(--gold-color);
  color: var(--gold-color);
  padding: 10px 25px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.clear-filters-btn:hover {
  background: var(--gold-color);
  color: #fff;
  transform: translateY(-2px);
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 2rem;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.search-group {
  grid-column: span 1;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}

.search-input,
.filter-select {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.search-input {
  width: 100%;
}

/* Jobs Section */
.jobs-section {
  margin-top: 2rem;
}

.jobs-header {
  margin-bottom: 2rem;
}

.jobs-count {
    text-align: center;
    background: #be940b18;
    padding: 10px 20px;
    width: fit-content;
    margin: 0 auto;
    border-radius: 50px;
    border: 1px solid #be930c;
}

.jobs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 2rem;
}

.job-card {
  background: white;
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.job-card:nth-child(5n+1) .color-box {
  background: #ffe1cb;
}
.job-card:nth-child(5n+2) .color-box {
  background: #d5f6ed;
}
.job-card:nth-child(5n+3) .color-box {
  background: #e2dbf9;
}
.job-card:nth-child(5n+4) .color-box {
  background: #e0f3ff;
}
.job-card:nth-child(5n+5) .color-box {
  background: #fbe2f3;
}

.job-card.featured {
  border-color: var(--gold-color);
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, white 100%);
}
.job-card .color-box {
      background: #ffe1cb;
    padding: 20px;
    border-radius: 20px;
}

.job-card.expiring-soon {
  border-color: #ff6b6b;
}

.featured-badge,
.expiring-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.featured-badge {
  background: var(--gold-color);
  color: #333;
}

.expiring-badge {
  background: #ff6b6b;
  color: white;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
    margin-bottom: 10px;
  border-bottom: 1px solid #ffffff;
  padding-bottom: 10px;
}

.company-info {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex: 1;
}

.company-logo {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  overflow: hidden;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.company-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.company-details {
  flex: 1;
}

.job-title {
  margin: 0;
}
.detail-item {
  color: #000;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}
.company-name { 
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}
.job-details .location i {
  margin-right: 3px;
}
.job-type-badge {
  background: #fff;
  color: #000;
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  white-space: nowrap;
}

.type-icon {
  font-size: 1rem;
}

.job-details {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 14px;
}
.detail-icon {
  font-size: 1rem;
}

.job-description {
  margin-bottom: 1.5rem;
}

.job-description p {
  margin: 0;
  font-size: 14px;
}


.requirements-title {
  margin: 0 0 0.75rem 0;
}

.requirements-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
.job-card:nth-child(5n+1) .color-box  .requirement-tag {
    border: 1px solid rgb(***********);
}
.job-card:nth-child(5n+2) .color-box  .requirement-tag {
    border: 1px solid #78d7bd;
}
.job-card:nth-child(5n+3) .color-box  .requirement-tag {
    border: 1px solid #c3b4f6;
}
.job-card:nth-child(5n+4) .color-box  .requirement-tag {
    border: 1px solid #95d6ff;
}
.job-card:nth-child(5n+5) .color-box  .requirement-tag {
    border: 1px solid #ffa7e3;
}
.requirement-tag {
  background: transparent;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.job-footer {
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 20px 15px 10px;
}

.job-dates {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.posted-date,
.end-date {
  font-size: 0.8rem;
}

.apply-btn {
  line-height: normal;
  background: linear-gradient(45deg, var(--purple-color), var(--gold-color));
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.apply-btn:hover {
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 1rem;
}

/* No Jobs Found */
.no-jobs {
  text-align: center;
  padding: 4rem 2rem;
}

.no-jobs-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.no-jobs h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.no-jobs p {
  margin-bottom: 2rem;
}

/* Application Dialog */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.application-dialog {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  background-color: #bc871014;
}

.dialog-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}
.pop-title {
  margin: 0;
}
.dialog-icon i {
    font-size: 40px;
    color: var(--gold-color);
}

.dialog-title h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.dialog-title p {
  margin: 0;
  font-size: 0.9rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.application-form {
  padding: 1rem 2rem;
}

.form-section {
  margin-bottom: 15px;
}

.section-title {
  margin-bottom: 1rem;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}


.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.file-upload-section {
  display: flex;
  gap: 1rem;
}

.file-upload-group {
  flex: 1;
}

.file-upload-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px dashed #e0e0e0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.file-upload-label:hover {
  border-color: var(--gold-color);
  background: rgba(255, 215, 0, 0.05);
}

.file-input {
  display: none;
}

.upload-icon {
  font-size: 1.5rem;
}

.file-help {
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 20px 0 10px;
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
  padding: 10px 20px;
  line-height: normal;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  color: #333;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, var(--darkgold-color), var(--gold-color));
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  line-height: normal;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
}
.month-sub {
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .search-group {
    grid-column: span 2;
  }

  .jobs-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .hero-stats {
      gap: 10px;
      margin-top: 20px;
  }
  .stat-divider {
    width: 40px;
    height: 1px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .search-group {
    grid-column: span 1;
  }

  .filters-header {
    flex-direction: column;
    gap: 1rem;
  }

  .job-header {
    flex-direction: column;
    gap: 1rem;
  }

  .company-info {
   flex-direction: column-reverse;
        gap: 10px;
        align-items: start  
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .application-dialog {
    width: 95%;
    margin: 1rem;
  }
  .careers-content {
    padding: 2rem 0;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .application-form {
    padding: 1rem;
  }

  .dialog-header {
    padding: 1.5rem 1rem 1rem;
  }
}
