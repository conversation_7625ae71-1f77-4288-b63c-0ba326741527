:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.register-page {
  min-height: 100vh;
}

/* Hero Section */
.register-hero {
  position: relative;
  min-height: 40vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Main Content */
.register-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.form-wrapper {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 4rem;
  align-items: start;
}

/* Form Container */
.form-container {
  background: white;
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.form-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--gold-color), var(--purple-color));
}

/* Form Header */
.form-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.form-icon {
  font-size: 3rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.form-title {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.form-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

/* Form Styles */
.registration-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}


.form-input:valid {
  border-color: #28a745;
}

.form-input:invalid:not(:placeholder-shown) {
  border-color: #dc3545;
}

/* Error Messages */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.error-icon {
  font-size: 0.9rem;
}

/* Password Strength */
.password-strength {
  margin-top: 0.5rem;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.strength-fill {
  height: 100%;
  width: 60%;
  background: linear-gradient(90deg, #ffc107, #28a745);
  transition: width 0.3s ease;
}

.strength-text {
  font-size: 0.8rem;
  color: #666;
}


.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
  font-size: 0.9rem;
  line-height: 1.5;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-input:checked + .checkbox-custom {
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  border-color: var(--gold-color);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
}

.checkbox-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
}

.checkbox-icon {
  font-size: 1rem;
}


.terms-text {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.terms-link {
  color: var(--purple-color);
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

/* Form Actions */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.submit-btn,
.cancel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-btn {
  background: linear-gradient(45deg,#bc8710,#c1a407);
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-3px);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-btn {
  background: transparent;
  border: 2px solid #ddd;
  color: #666;
}

.cancel-btn:hover {
  border-color: #999;
  color: #333;
  background: #f8f9fa;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-icon {
  font-size: 1.2rem;
}

/* Login Link */
.login-link-section {
  text-align: center;
  margin-top: 1rem;
  padding-top: 2rem;
  border-top: 1px solid #e0e0e0;
}

.login-text {
  color: #666;
  margin: 0;
}

.login-link {
  color: var(--purple-color);
  text-decoration: none;
  font-weight: 600;
}

.login-link:hover {
  text-decoration: underline;
}

/* Side Panel */
.side-panel {
  background: linear-gradient(135deg, var(--gold-color), var(--purple-color));
  border-radius: 20px;
  padding: 2.5rem;
  color: white;
  position: sticky;
  top: 100px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.panel-content {
  text-align: center;
}
.benefit-content h6 {
  margin-bottom: 3px;
}
.panel-icon {
  font-size: 3rem;
  line-height: normal;
  margin-bottom: 1.5rem;
}

.panel-title {
  margin-bottom: 2rem;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.benefit-icon {
    font-size: 30px;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}
.carousel-control-prev {
    left: -30px;
}
.carousel-control-next {
   right: -30px;
}

.benefit-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.benefit-content p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-wrapper {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .side-panel {
    position: static;
  }
}

@media (max-width: 768px) {

  .form-row {
    grid-template-columns: 1fr;
  }

  .form-container {
    padding: 2rem;
  }

  .side-panel {
    padding: 2rem;
  }

  .benefits-list {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .form-container {
    padding: 1.5rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .form-actions {
    gap: 0.75rem;
  }

  .submit-btn,
  .cancel-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}
