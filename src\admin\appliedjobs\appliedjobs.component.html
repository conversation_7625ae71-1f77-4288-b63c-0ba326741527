<div class="applied-jobs-container p-4">
  <h5 class="mb-4 border-bottom pb-2">Applied Jobs</h5>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <!-- Applied Jobs Table -->
  <div class="table-wrapper" *ngIf="appliedJobs.length > 0">
    <table class="table">
      <thead>
        <tr>
          <th>#</th>
          <th>Employer Name</th>
          <th>Email</th>
          <th>Number</th>
          <th>Job Title</th>
          <th>Cover Letter</th>
          <th>Applied On</th>
          <th>Resume</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let job of appliedJobs; index as i">
          <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
          <td>{{ job.employerName }}</td>
          <td>{{ job.email || 'N/A' }}</td>
          <td>{{ job.phoneNumber || 'N/A' }}</td>
          <td>{{ job.jobEntity?.jobTitle || 'N/A' }}</td>
          <td>{{ job.coverLetterPath || 'N/A' }}</td>
          <td>{{ job.creationTime | date: 'dd/MM/yyyy' }}</td>
          <td>
            <a [href]="job.resumePath" target="_blank" class="view-resume-link">
              View Resume
            </a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- No Applied Jobs -->
  <div *ngIf="appliedJobs.length === 0 && !errorMessage" class="no-data">
    <p>😕 No applied jobs found.</p>
  </div>

  <!-- Pagination -->
  <div class="pagination-controls" *ngIf="totalCount > pageSize">
    <button (click)="prevPage()" [disabled]="pageNumber === 1" class="pagination-btn">
      ⬅ Previous
    </button>
    <span class="pagination-info">Page {{ pageNumber }}</span>
    <button (click)="nextPage()" [disabled]="(pageNumber * pageSize) >= totalCount" class="pagination-btn">
      Next ➡
    </button>
  </div>
</div>
