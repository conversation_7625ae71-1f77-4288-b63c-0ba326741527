import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class JobService {
  incrementApplication(jobId: any) {
    throw new Error('Method not implemented.');
  }

   private baseUrl = 'https://30lss7df-5001.inc1.devtunnels.ms/api/jobs';

  constructor(private http: HttpClient) {}

  getJobs(): Observable<any> {
    return this.http.get<any>(this.baseUrl);
  }

  getJobById(id: number): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/${id}`);
  }

  createJob(jobData: any): Observable<any> {
    return this.http.post<any>(this.baseUrl, jobData);
  }

  updateJob(jobId: number, job: any): Observable<any> {
    return this.http.put(`${this.baseUrl}/${jobId}`, job);
  }

   deleteJob(id: number): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`);
  }

}
