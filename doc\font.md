Today there are four font container formats in use on the web: EOT, TTF, WOFF, and WOFF2.

Unfortunately, despite the wide range of choices, there isn't a single universal format that works across all old and new browsers:

EOT is IE only,
TTF has partial IE support,
WOFF enjoys the widest support but is not available in some older browsers
WOFF 2.0 support is a work in progress for many browsers.
If you want your web app to have the same font across all browsers then you might want to provide all 4 font type in CSS declarations:

 @font-face {
      font-family: 'besom' !important;
      src: url('fonts/besom/besom.eot');
      src: url('fonts/besom/besom.eot?#iefix') format('embedded-opentype'),
           url('fonts/besom/besom.woff2') format('woff2'),
           url('fonts/besom/besom.woff') format('woff'),
           url('fonts/besom/besom.ttf') format('truetype'),
           url('fonts/besom/besom.svg#besom_2regular') format('svg');
      font-weight: normal;
      font-style: normal;
  }



  A PSD file, standing for "Photoshop Document," is the native file format of Adobe Photoshop, designed to store layered images and all their editing elements, allowing for flexible image manipulation and editing