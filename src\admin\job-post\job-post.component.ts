import { <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { JobService } from '../../services/job.service';
import { Title } from '@angular/platform-browser';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { Job } from '../../models/job.model';
declare var bootstrap: any;

@Component({
  selector: 'app-job-post',
  standalone: true,
  imports: [FormsModule,NgFor,NgIf],
  templateUrl: './job-post.component.html',
  styleUrl: './job-post.component.css'
})
export class JobPostComponent {

  jobs: any[] = []; 
  showCreateForm = false;
  job: Job = {
    jobId:null,
    jobTitle: '',
    company: '',
    description: '',
    jobType: '',
    location: '',
    mode: '',
    salaryPackage: null,
    requirements: ''
  };
  modalMode: 'create' | 'edit' = 'create';

 constructor(private jobService: JobService,private pageTitle: PageTitleService) {}

  ngOnInit(): void {
      this.pageTitle.setTitle('Job Post');
    this.loadJobs();
  }

 loadJobs() {
  this.jobService.getJobs().subscribe({
    next: (data) => {
     this.jobs = data.items || data || [];
 
      console.log('Jobs loaded:', data);
    },
    error: (err) => console.error('Error loading jobs', err),
    complete: () => console.log('Job loading complete') 
  });
}

  toggleCreateForm() {
    this.showCreateForm = !this.showCreateForm;
  }

    postJob() {
    console.log('Posting job:', this.job);
    if (!this.isFormValid()) {
      alert('Please fill all fields correctly');
      return;
    }

    const jobToSend = { ...this.job };
    jobToSend.jobId = null;

    this.jobService.createJob(jobToSend).subscribe({
      next: (newJob) => {
        this.jobs.push(newJob);
        this.resetForm();
        this.handleSuccess();
        console.log('Job posted successfully:', newJob);
      },
      error: (err) => console.error('Error posting job', err)
    });
  }

   updateJob() {
    console.log('Updating job:', this.job);
    if (this.isFormValid()) {
      if (this.job.jobId !== null && this.job.jobId !== undefined) {
        this.jobService.updateJob(this.job.jobId, this.job).subscribe({
          next: (updatedJob) => {
            const index = this.jobs.findIndex(j => j.jobId === updatedJob.jobId);
            if (index !== -1) {
              this.jobs[index] = updatedJob;
            }
            console.log('Job updated successfully:', updatedJob);
            this.handleSuccess();
          },
          error: (err) => console.error('Error updating job', err)
        });
      } else {
        alert('Invalid job ID');
      }
    } else {
      alert('Please fill all fields');
    }
  }

 openModal(mode: 'create' | 'edit', jobData: any = null) {
  this.modalMode = mode;
  if (mode === 'edit' && jobData && typeof jobData === 'object' && !Array.isArray(jobData)) {
    this.job = { ...jobData };
  } else {
    this.resetForm();
  }
 }
  resetForm() {
    this.job = {
      jobId:null,
      jobTitle: '',
      company: '',
      description: '',
      jobType: '',
      location: '',
      mode: '',
      salaryPackage: null,
      requirements: ''
    };
  }

  deleteJob(index: number, jobId: number) {
    this.jobService.deleteJob(jobId).subscribe({
      next: () => {
        this.jobs.splice(index, 1);
      },
      error: (err) => console.error('Error deleting job', err)
    });
  }

   private isFormValid(): boolean {
    const {
      jobTitle, company, description, jobType, location, mode, salaryPackage
    } = this.job;

    return !!(jobTitle && company && description && jobType && location && mode && salaryPackage != null);
  }

  private handleSuccess() {
    this.showCreateForm = false;
    const modalElement = document.getElementById('jobModal');
    if (modalElement) {
      let bsModal = bootstrap.Modal.getInstance(modalElement);
      if (!bsModal) {
        bsModal = new bootstrap.Modal(modalElement);
      }
      bsModal.hide();
      this.closeModal();
    }
 
  }


  closeModal() {
  const modalElement = document.getElementById('jobModal');
  const modalInstance = bootstrap.Modal.getInstance(modalElement);

  if (modalInstance) {
    modalInstance.hide();
  }

  setTimeout(() => {
    document.body.classList.remove('modal-open');
    document.body.style.removeProperty('overflow');
    document.body.style.removeProperty('padding-right');

    document.querySelectorAll('.modal-backdrop').forEach(b => b.remove());}, 500);
  }
}
