{"name": "insizon-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "bootstrap": "^5.3.7", "ngx-toastr": "^19.0.0", "primeicons": "^7.0.0", "primeng": "^17.18.12", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.15", "@angular/cli": "^18.2.15", "@angular/compiler-cli": "^18.2.0", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~5.1.0", "angular-eslint": "19.2.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-unused-imports": "^4.1.4", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}, "engines": {"node": "18.20.6"}, "resolutions": {"conflicting-package": "desired-version"}}