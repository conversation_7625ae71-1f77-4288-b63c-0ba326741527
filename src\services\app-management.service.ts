import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AppManagementService {

  private baseUrl = 'https://30lss7df-5001.inc1.devtunnels.ms/api/apps';
  
  constructor(private http: HttpClient) {}
  
  getApps(queryParams: { [key: string]: any } = {}): Observable<any> {
    return this.http.get<any>(this.baseUrl, {
    params: queryParams
    });
   }

   getAllApps(): Observable<any[]> {
   return this.http.get<any[]>(`${this.baseUrl}/all`);
  }
  
    getAppById(id: number): Observable<any> {
      return this.http.get<any>(`${this.baseUrl}/${id}`);
    }
  
    createApp(appData: any): Observable<any> {
      return this.http.post<any>(this.baseUrl, appData);
    }
  
    updateApp(jobId: number, job: any): Observable<any> {
      return this.http.put(`${this.baseUrl}/${jobId}`, job);
    }
  
    deleteApp(id: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`);
    }
}
