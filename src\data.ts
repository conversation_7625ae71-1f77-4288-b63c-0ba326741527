




interface IKeywords {
  id: number;
  name: string;
  txt1: string;
}


const KeywordData: Array<IKeywords> = [
  {
    id: 0,
    name: "App Name",
    txt1: `Insizon, Insizon LLC, Insizon Software Company, Insizon LLC Company, Insizon LLC Software Company`
  },
  {
    id: 1,
    name: "Author",
    txt1: `Insizon, LLC`
  },
  {
    id: 2,
    name: "Competition",
    txt1: `Software Company, IOS App, Interesting Company`
  },
]