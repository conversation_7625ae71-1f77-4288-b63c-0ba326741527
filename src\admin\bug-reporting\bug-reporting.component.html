<div class="p-4" style="background: #f9f9f9cc; border-radius: 12px;">
  <div class="header-section border-bottom pb-2">
    <h5 class="m-0">Reported Bugs</h5>
    <button class="btn btn-toggle" (click)="toggleView()">
      Switch to {{ viewMode === 'card' ? 'Table View' : 'Card View' }}
    </button>
  </div>

  <!-- 🃏 Card View -->
  <div *ngIf="viewMode === 'card'" class="card-container">
    <div class="bug-card" *ngFor="let bug of bugs">
      <div class="card-content">
         <div class="badges-container mb-3">
            <span class="badge priority-badge" [ngClass]="getPriorityClass(bug.priority || 'Low')">
              {{ bug.priority || 'Low' }}
            </span>
            <span class="badge status-badge" [ngClass]="getStatusClass(bug.status || 'open')">
              {{ bug.status }}
            </span>
          </div>
        <h6 class="card-title mb-2">{{ bug.title }}</h6>
        <div class="card-info">
          <p><strong>Date:</strong> {{ bug.createdAt | date:'yyyy-MM-dd' }}</p>
        </div>
        <button class="btn btn-view" (click)="viewBug(bug)">View Bug</button>
      </div>
    </div>
  </div>

  <!-- 📋 Table View -->
  <div *ngIf="viewMode === 'table'" class="table-container">
    <div class="table-responsive">
      <table class="custom-table">
        <thead>
          <tr>
            <th>#</th>
            <th>Title</th>
            <th>App Name</th>
            <th>Priority</th>
            <th>Status</th>
            <th>Date</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let bug of bugs; let i = index">
            <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
            <td>{{ bug.title }}</td>
            <td>{{ bug.appManagement?.title }}</td>
            <td>
              <span class="badge priority-badge" [ngClass]="getPriorityClass(bug.priority || 'Low')">
                {{ bug.priority }}
              </span>
            </td>
            <td>
              <span class="badge status-badge" [ngClass]="getStatusClass(bug.status || 'open')">
                {{ bug.status }}
              </span>
            </td>
            <td>{{ bug.createdAt | date:'yyyy-MM-dd' }}</td>
            <td>
              <button class="btn btn-view btn-sm" (click)="viewBug(bug)">View</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages > 1">
    <button class="btn btn-light" [disabled]="pageNumber === 1" (click)="changePage(pageNumber - 1)">
      Previous
    </button>

    <button
      *ngFor="let page of [].constructor(totalPages); let i = index"
      class="btn"
      [ngClass]="{ 'btn-primary': pageNumber === (i + 1), 'btn-light': pageNumber !== (i + 1) }"
      (click)="changePage(i + 1)"
    >
      {{ i + 1 }}
    </button>

    <button class="btn btn-light" [disabled]="pageNumber === totalPages" (click)="changePage(pageNumber + 1)">
      Next
    </button>
  </div>

  <!-- Modal Popup -->
  <div class="modal-overlay" *ngIf="showModal" (click)="closeModal()">
    <div class="modal-content" (click)="$event.stopPropagation()" *ngIf="selectedBug">
      <div class="modal-header border-bottom pb-2">
        <h6 class="m-0">Bug Details</h6>
        <button class="close-btn" (click)="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label><strong>Title:</strong></label>
          <input type="text" [(ngModel)]="selectedBug.title" class="form-control">
        </div>
        <div class="form-group">
          <label><strong>Description:</strong></label>
          <textarea [(ngModel)]="selectedBug.description" class="form-control" rows="3"></textarea>
        </div>
       
          <div class="form-group custom-select-wrapper">
            <label><strong>Priority:</strong></label>
            <select [(ngModel)]="selectedBug.priority" class="form-control custom-select">
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
          <div class="form-group custom-select-wrapper">
            <label><strong>Status:</strong></label>
            <select [(ngModel)]="selectedBug.status" class="form-control custom-select">
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="Closed">Closed</option>
            </select>
          </div>  
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" (click)="closeModal()">Cancel</button>
        <button class="btn btn-primary" (click)="updateBug()">Update Bug</button>
      </div>
    </div>
  </div>
</div>
