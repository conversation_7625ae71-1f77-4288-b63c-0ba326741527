{"root": true, "ignorePatterns": ["projects/**/*"], "plugins": ["unused-imports"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:prettier/recommended"], "rules": {"no-unused-vars": "off", "unused-imports/no-unused-imports": "error", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}]}}, {"files": ["*.html"], "excludedFiles": ["*inline-template-*.component.html"], "extends": ["plugin:@angular-eslint/template/recommended", "plugin:@angular-eslint/template/accessibility", "plugin:prettier/recommended"], "rules": {"prettier/prettier": ["error", {"parser": "angular"}]}}]}