# There are a few ways to change the port Angular uses: 



## Using the ng serve command: 
	• To run the application on a specific port for the current session, use the command: 

    ng serve --port <port_number>

Replace <port_number> with the desired port, such as 4201 or 8080. 


## Modifying angular.json: 
	• To permanently change the default port for your project, open the angular.json file in the root directory. 
	• Locate the projects section, then find your project and the architect section. 
	• Within architect, find the serve target. 
	• Add or modify the port option within the options block: 

    "serve": {
        "builder": "@angular-devkit/build-angular:dev-server",
        "options": {
            "browserTarget": "<your-project-name>:build",
            "port": <port_number>
        },
    }

Replace <port_number> with the desired port. 

## Using package.json: 
	• Open the package.json file and find the scripts section. 
	• Modify the start script to include the --port option: 

    "scripts": {
        "start": "ng serve --port <port_number>"
    }

Replace <port_number> with the desired port. 

##  Potential Port Conflicts: 
	• If you encounter an "address already in use" error, it means another application is using the desired port. 
	• Use the command netstat -ano | findstr :<port_number> to identify the process using the port (Windows). 
	• Use the command lsof -i :<port_number> to identify the process using the port (Linux or macOS). 
	• Terminate the process using the identified PID or choose a different port. 
